import React from "react";
import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { <PERSON>aS<PERSON>ch, Fa<PERSON><PERSON>, FaCheck, FaBars, FaTimes } from "react-icons/fa";
import { Settings, Settings2 } from "lucide-react";
import { usePrivy, useWallets, useSignupWithPasskey } from '@privy-io/react-auth';
import SearchModal from "./SearchModal";
import SettingsDropdown from "../../components/SettingsDropdown";
import RecoveryKeyModal from "../../components/RecoveryKeyModal";
import ImportWalletModal from "../../components/ImportWalletModal";
import ManageWalletsModal from "../../components/ManageWalletsModal";
// SVG imports
import NotificationIcon from "../../assets/notification.svg";
import HelpIcon from "../../assets/help.svg";
import DocumentsIcon from "../../assets/documents.svg";
import SettingsIcon from "../../assets/settings.svg";
import ProfileIcon from "../../assets/profile.svg";
import Languages from "../../assets/language.svg";

// Define types for wallet addresses
interface TokenAddress {
  address: string;
  type: string;
  chainName: string;
  walletType?: string;
}

interface AddressesByChain {
  ethereum: TokenAddress[];
  solana: TokenAddress[];
}

function LinkPasskey() {
  const { signupWithPasskey } = useSignupWithPasskey();

  return (
    <button 
      onClick={() => signupWithPasskey()}
      className="w-full text-left text-sm text-white hover:text-gray-300 transition-colors px-4 py-2"
    >
      Add Passkey to Account
    </button>
  );
}

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<string>("");
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
  const [isSearchOpen, setIsSearchOpen] = useState<boolean>(false);
  const [isProfileOpen, setIsProfileOpen] = useState<boolean>(false);
  const { user, logout, authenticated, connectWallet } = usePrivy();
  const { wallets } = useWallets();
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);
  const [userAddress, setUserAddress] = useState<string | null>(null);
  const [isRecoveryKeyModalOpen, setIsRecoveryKeyModalOpen] = useState<boolean>(false);
  const [isImportWalletModalOpen, setIsImportWalletModalOpen] = useState<boolean>(false);
  const [isManageWalletsModalOpen, setIsManageWalletsModalOpen] = useState<boolean>(false);
  
  // Debug wallet state
  useEffect(() => {
    if (authenticated) {
      console.log("Navbar - User authenticated:", authenticated);
      console.log("Navbar - User object:", user);
      if (user?.email) {
        console.log("Navbar - User email:", user.email);
      }
      console.log("Navbar - Wallets:", wallets);
      
      // Check if wallets array exists and has entries
      if (wallets && wallets.length > 0) {
        console.log("Navbar - Wallet details:", wallets.map(w => ({
          address: w.address,
          type: w.walletClientType,
          connected: w.connectedAt, // Fixed: changed connected to connectedAt
          chainId: w.chainId
          // Removed chains property
        })));
        
        // Set the first wallet address as the user address for display
        if (wallets[0]?.address) {
          setUserAddress(wallets[0].address);
          console.log("User address set from wallet:", wallets[0].address);
        }
      } else {
        console.log("Navbar - No wallets available");
      }
    }
  }, [authenticated, user, wallets]);

  // ✅ Restore activeTab from localStorage on mount
  useEffect(() => {
    const storedTab = localStorage.getItem("activeTab");
    if (storedTab) {
      setActiveTab(storedTab);
    }
  }, []);

  // Helper function to validate if a string looks like a valid blockchain address
  const isValidBlockchainAddress = (address: string): boolean => {
    if (!address || typeof address !== 'string') return false;
    
    // Ethereum addresses are 42 chars (0x + 40 hex chars)
    if (address.startsWith('0x')) {
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
    
    // Solana addresses are 32-44 chars (base58 encoding)
    // Exclude anything that looks like an email
    if (address.includes('@') || address.includes('.com') || address.includes('.io') || address.includes('.org')) {
      console.log(`Rejecting email-like address: ${address}`);
      return false;
    }
    
    // Basic check for Solana addresses - typically 32-44 chars base58
    return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
  };
  
  // Helper function to determine blockchain type from address format
  const getBlockchainTypeFromAddress = (address: string): { type: string; chainName: string } | null => {
    if (!address || typeof address !== 'string') return null;
    
    // Debug log for address detection
    // console.log(`Analyzing address format for: ${address.slice(0, 6)}...${address.slice(-4)}`);
    
    // Ethereum addresses always start with 0x followed by 40 hex chars
    if (/^0x[a-fA-F0-9]{40}$/.test(address)) {
      // console.log(`Identified as Ethereum address: ${address.slice(0, 6)}...${address.slice(-4)}`);
      return {
        type: 'ethereum',
        chainName: 'Ethereum'
      };
    }
    
    // Specific handling for HzHsCX address (known Solana)
    if (address.startsWith('HzHsCX')) {
      // console.log(`Known Solana address format detected: ${address.slice(0, 6)}...${address.slice(-4)}`);
      return {
        type: 'solana',
        chainName: 'Solana'
      };
    }
    
    // Solana addresses don't start with 0x and typically use base58 encoding
    if (!address.startsWith('0x')) {
      // console.log(`Non-0x address detected, likely Solana: ${address.slice(0, 6)}...${address.slice(-4)}`);
      return {
        type: 'solana',
        chainName: 'Solana'
      };
    }
    
    console.log(`Cannot determine blockchain type for address: ${address}`);
    return null;
  };

  // Function to get all available wallet addresses
  const getWalletAddresses = (): TokenAddress[] => {
    // Track addresses for deduplication
    const addressMap = new Map();
    const addresses: TokenAddress[] = [];
    const seenAddresses = new Set();
    
    console.log("Getting wallet addresses from all sources...");
    
    // Helper to add an address to our results
    const addUniqueAddress = (address: string, type: string, chainName: string, walletType: string = "external"): boolean => {
      // First validate this is actually a blockchain address
      if (!isValidBlockchainAddress(address)) {
        console.log(`Rejecting invalid address format: ${address}`);
        return false;
      }
      
      // Force special case detection for known Solana addresses
      if (address.startsWith('984zc')) {
        console.log(`Overriding detection for known Solana address: ${address.slice(0, 6)}...${address.slice(-4)}`);
        type = 'solana';
        chainName = 'Solana';
      }
      
      // console.log(`Found address: ${address} (${type}, ${chainName}, ${walletType})`);
      
      const normalizedAddress = address.toLowerCase();
      
      // Skip if we've already added this address
      if (seenAddresses.has(normalizedAddress)) {
        console.log(`Skipping duplicate address: ${address}`);
        return false;
      }
      
      // MODIFIED FILTER: Allow smart wallets and ANY Solana wallet
      const isSolanaWallet = type === 'solana';
      
      if (walletType !== "smart" && !isSolanaWallet) {
        console.log(`Skipping non-smart wallet that's not a Solana wallet: ${address} (${walletType}, ${type})`);
        return false;
      }
      
      // console.log(`Adding wallet address: ${address} (${type}, ${chainName}, ${walletType})`);
      seenAddresses.add(normalizedAddress);
      
      addresses.push({
        address: address,
        type: type,
        chainName: chainName,
        walletType: walletType
      });
      
      return true;
    };
    
    // Then check linked accounts from user object - look specifically for smart wallets
    if (user?.linkedAccounts && user.linkedAccounts.length > 0) {
      console.log(`Found ${user.linkedAccounts.length} linked accounts in user object`);
      
      for (const account of user.linkedAccounts) {
        // Check if account has an address property safely
        const accountAddress = (account as any).address;
        
        if (accountAddress) {
          // Skip email accounts completely
          if (accountAddress.includes('@')) {
            console.log(`Skipping email address: ${accountAddress}`);
            continue;
          }
          
          // Simple type-based filtering that directly uses the account type
          const accountType = (account as any).type;
          
          if (accountType === 'smart_wallet') {
            // This is a smart wallet
            // console.log(`Found smart wallet: ${accountAddress.slice(0, 6)}...${accountAddress.slice(-4)}`);
            
            // Determine blockchain type from address format (most smart wallets are Ethereum-based)
            const blockchainInfo = getBlockchainTypeFromAddress(accountAddress);
            
            if (blockchainInfo) {
              addUniqueAddress(accountAddress, blockchainInfo.type, blockchainInfo.chainName, 'smart');
            }
          } 
          else if (accountType === 'wallet') {
            // Check address format to determine if it's a Solana wallet
            const isSolanaWallet = !accountAddress.startsWith('0x');
            
            // Determine the blockchain type
            const blockchainInfo = getBlockchainTypeFromAddress(accountAddress);
            
            if (blockchainInfo) {
              // If it's a Solana wallet or a smart wallet, add it
              if (isSolanaWallet) {
                console.log(`Found Solana wallet: ${accountAddress.slice(0, 6)}...${accountAddress.slice(-4)}`);
                addUniqueAddress(accountAddress, 'solana', 'Solana', 'external');
              } else {
                // For other wallet types, only include smart wallets
                console.log(`Skipping regular Ethereum wallet: ${accountAddress.slice(0, 6)}...${accountAddress.slice(-4)}`);
              }
            }
          }
          else {
            console.log(`Skipping non-wallet linked account of type: ${accountType}`);
          }
        }
      }
    }
    
    // Check wallets from useWallets hook - include smart wallets and Solana wallets
    if (wallets && wallets.length > 0) {
      const connectedWallets = wallets.filter(w => {
        // Check if it's connected
        if (!w.connectedAt) return false;
        
        // Determine if this is a Solana wallet
        const isSolana = w.address ? !w.address.startsWith('0x') : false;
        
        // Check if this is a smart wallet
        const isSmartWalletType = w.walletClientType === 'smart';
        const hasSmartWalletProperty = (w as any).isSmartWallet === true || 
                                      (w as any).smartContractAddress || 
                                      (w as any).accountAbstraction;
        
        // Include if it's a smart wallet OR any Solana wallet (not just embedded ones)
        return (isSmartWalletType || hasSmartWalletProperty) || isSolana;
      });
      
      console.log(`Found ${connectedWallets.length} wallets from useWallets hook (smart wallets + Solana wallets)`);
      
      connectedWallets.forEach(wallet => {
        if (wallet.address) {
          // Skip email-like addresses
          if (wallet.address.includes('@')) {
            console.log(`Skipping email-like address from wallet: ${wallet.address}`);
            return;
          }
          
          // Try to determine blockchain type from wallet metadata first
          let blockchainInfo = null;
          
          // Check for Phantom wallet - solana chains cannot be checked due to typing issues
          if (wallet.walletClientType === 'phantom') {
            blockchainInfo = {
              type: 'solana',
              chainName: 'Solana'
            };
          } else {
            // Fallback to address format detection
            blockchainInfo = getBlockchainTypeFromAddress(wallet.address);
          }
          
          if (!blockchainInfo) {
            console.log(`Skipping wallet with unidentifiable address format: ${wallet.address}`);
            return;
          }
          
          // Get chain name, potentially overriding with chain ID info for EVMs
          let chainName = blockchainInfo.chainName;
          
          // For Ethereum addresses, we can further specify the exact EVM chain
          if (blockchainInfo.type === 'ethereum' && wallet.chainId) {
            const chainIdNum = parseInt(wallet.chainId);
            if (chainIdNum === 8453) {
              chainName = 'Base';
            } else if (chainIdNum === 137) {
              chainName = 'Polygon';
            }
          }
          
          console.log(`Processing wallet: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)} (${chainName}, ${wallet.walletClientType})`);
          
          // Determine wallet type based on attributes
          let walletType = wallet.walletClientType || 'external';
          
          // Set the wallet type correctly in the results
          addUniqueAddress(wallet.address, blockchainInfo.type, chainName, walletType);
        }
      });
    }
    
    console.log("Final unique addresses:", addresses);
    return addresses;
  };

  // Copy wallet address to clipboard
  const copyToClipboard = (address: string): void => {
    navigator.clipboard.writeText(address).then(() => {
      setCopiedAddress(address);
      // Reset copy icon after 2 seconds
      setTimeout(() => setCopiedAddress(null), 2000);
    });
  };

  // Helper function to get user display info
  const getUserDisplayInfo = () => {
    if (!authenticated || !user) return { displayName: "Guest", address: null, userId: null };
    
    // Debug user object to find email
    // console.log("Getting user display info from:", user);
    
    // Check all possible locations for email
    let email = null;
    
    // Check email property - handle case where it's an object
    if (user.email?.address) {
      email = user.email.address.toString();
      // console.log("Found email:", email);
    } 
    // Check linked accounts for email
    else if (user.linkedAccounts && user.linkedAccounts.length > 0) {
      const emailAccount = user.linkedAccounts.find(account => 
        account.type === 'email' || ((account as any).address && (account as any).address.includes('@'))
      );
      if (emailAccount) {
        email = (emailAccount as any).address || (emailAccount as any).email;
        console.log("Found email in linkedAccounts:", email);
      }
    }
    
    const displayName = "Connected User";
    
    // Get user ID and clean it up - remove did:privy: prefix
    let rawUserId = user.id || (user as any).userId || user.wallet?.address;
    let userId = rawUserId;
    
    // Remove did:privy: prefix if it exists
    if (typeof userId === 'string' && userId.startsWith('did:privy:')) {
      userId = userId.replace('did:privy:', '');
    }
    
    console.log("Raw user ID:", rawUserId);
    console.log("Cleaned user ID:", userId);
    
    return { displayName, userId, email };
  };

  useEffect(() => {
    const currentPath = location.pathname;
  
    const tabMap: Record<string, string> = {
      "/": "Home",
      "/pulse": "Pulse",
      "/portfolio": "Portfolio",
      "/vault": "Vault",
    };
  
    // Set tab to "Spot" only if route is exactly "/trade"
    if (currentPath.startsWith("/trade/")) {
      setActiveTab("Spot");
      localStorage.setItem("activeTab", "Spot");
    }
    
    // If the route matches one of the others in tabMap
    else if (tabMap[currentPath]) {
      const activeRoute = tabMap[currentPath];
      setActiveTab(activeRoute);
      localStorage.setItem("activeTab", activeRoute);
    }
    // If it's something like /trade/abc123 or unknown, clear tab
    else {
      setActiveTab("");
      localStorage.removeItem("activeTab");
    }
  }, [location.pathname]);
  

  const handleTabClick = (tab: string): void => {
    setActiveTab(tab);
    localStorage.setItem("activeTab", tab);

    if (tab === "Spot") {
      navigate("/trade");
    } else if (tab === "Home") {
      navigate("/");
    } else if (tab === "Trade") {
      navigate("/trade/advanced");
    } else if (tab === "Invite") {
      navigate("/invite");
    }
    else if(tab=== "Pulse"){
      navigate("/pulse")
    }
    else if(tab==='Portfolio'){
      navigate("/portfolio")
    }
    else if(tab==='Vault'){
      navigate("/vault")
    }
  };

  const userInfo = getUserDisplayInfo();
  // console.log("User info object:", userInfo);
  // Get all available wallet addresses
  const walletAddresses = getWalletAddresses();
  
  // Group addresses by chain
  const getAddressesByChain = (): AddressesByChain => {
    const result: AddressesByChain = {
      ethereum: [],
      solana: []
    };
    
    // console.log("Grouping addresses by chain, total:", walletAddresses.length);
    
    walletAddresses.forEach(address => {
      // Special case: Always categorize HzHsCX as Solana
      if (address.address && address.address.startsWith('HzHsCX')) {
        // console.log(`OVERRIDE: Force categorizing ${address.address.slice(0, 6)}...${address.address.slice(-4)} as Solana`);
        result.solana.push({
          ...address,
          type: 'solana',
          chainName: 'Solana'
        });
        return;
      }
      
      // Only allow addresses starting with 0x in Ethereum section
      if (address.address && !address.address.startsWith('0x')) {
        // console.log(`OVERRIDE: Non-0x address detected, moving to Solana section: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
        result.solana.push({
          ...address,
          type: 'solana',
          chainName: 'Solana'
        });
        return;
      }
      
      const chainKey = address.chainName.toLowerCase();
      // console.log(`Categorizing address ${address.address.slice(0, 6)}...${address.address.slice(-4)} as ${chainKey}`);
      
      if (chainKey === 'ethereum') {
        // Double check - only 0x addresses should be in Ethereum
        if (address.address && address.address.startsWith('0x')) {
          result.ethereum.push(address);
        } else {
          console.log(`Moving non-0x address from Ethereum to Solana: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
          result.solana.push({
            ...address,
            type: 'solana',
            chainName: 'Solana'
          });
        }
      } else if (chainKey === 'solana') {
        result.solana.push(address);
      } else if (chainKey === 'base') {
        // Move Base addresses to Ethereum instead
        if (address.address && address.address.startsWith('0x')) {
          console.log(`Moving Base address to Ethereum: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
          result.ethereum.push({
            ...address,
            chainName: 'Ethereum'
          });
        } else {
          console.log(`Moving non-0x Base address to Solana: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
          result.solana.push({
            ...address,
            type: 'solana',
            chainName: 'Solana'
          });
        }
      } else {
        console.log(`Unknown chain for address: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
        
        // Default categorization based on address format
        if (address.address && address.address.startsWith('0x')) {
          console.log(`Defaulting 0x address to Ethereum: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
          result.ethereum.push(address);
        } else {
          console.log(`Defaulting non-0x address to Solana: ${address.address.slice(0, 6)}...${address.address.slice(-4)}`);
          result.solana.push({
            ...address,
            type: 'solana',
            chainName: 'Solana'
          });
        }
      }
    });
    
    console.log("Addresses by chain:", result);
    
    return result;
  };
  
  const addressesByChain = getAddressesByChain();
 
  // Helper function to format addresses
  const formatAddress = (address: string): string => {
    if (address.length <= 12) {
      return address;
    }
    const start = address.slice(0, 6);
    const end = address.slice(-4);
    return `${start}...${end}`;
  };
 
  return (
    <nav className="bg-[#141416] text-white flex flex-wrap items-center justify-between py-3 px-[20px]">
    {/* Mobile Menu Button (only visible on small screens) */}
    <div className="lg:hidden flex items-center">
      <button 
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        className="text-white p-2"
      >
        {mobileMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
      </button>
    </div>

    {/* Left: Logo and Navigation */}
    <div className="flex items-center">
      <div className="text-2xl font-bold flex items-center justify-center">
        <svg width="23" height="30" viewBox="0 0 23 30" fill="none" xmlns="http://www.w3.org/2000/svg" className="my-auto">
          <path d="M16.0018 0H0V12.3855L4.25571 8.39106V4.3054H14.6038C16.844 4.3054 18.6595 6.13966 18.6595 8.40317V12.9693C18.6595 15.2328 16.844 17.067 14.6038 17.067H7.06274L15.8442 8.4432H8.79709L0 17.013V21.4739L8.74088 30H15.811L7.01943 21.3696H16.0801C19.9027 21.3184 22.9769 18.0559 22.9852 14.1927L23 7.08566C23.0083 3.17505 19.8722 0 16.0018 0Z" fill="white"/>
        </svg>
      </div>
      
      {/* Divider - Only visible on desktop */}
      <div className="hidden lg:block border-r border-white h-6 mx-[25px]"></div>

      {/* Navigation Tabs - Desktop */}
      <ul className="hidden lg:flex items-center gap-[30px]">
  {["Home", "Spot",  "Portfolio", "Pulse"].map((tab) => (
    <li
      key={tab}
      onClick={() => handleTabClick(tab)}
      className={`px-[15px] py-2 rounded-3xl font-medium cursor-pointer 
        transition-all duration-300 ease-in-out transform
        ${
          activeTab === tab
            ? "bg-white text-gray-600 scale-105 shadow-md"
            : "text-white hover:scale-105 hover:text-gray-300"
        }`}
    >
      {tab}
    </li>
  ))}
</ul>

    </div>

    {/* Center: Search Bar - only on medium screens and up */}
    <div className="hidden md:block relative w-1/3 mx-4">
      <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500" />
      <input
        type="text"
        placeholder="Search..."
        className="bg-[#181C20] text-white pl-12 pr-4 py-3 rounded-3xl w-full outline-none"
        onClick={() => setIsSearchOpen(true)}
        readOnly
      />
    </div>

    {/* Mobile Search Icon - visible only on small screens */}
    <div className="md:hidden">
      <button 
        onClick={() => setIsSearchOpen(true)}
        className="p-2 rounded-full bg-[#181C20]"
      >
        <FaSearch className="text-gray-500" />
      </button>
    </div>

    {/* Right: Icons - Reduced set for mobile */}
    <div className="flex items-center gap-2 md:gap-4">
      {/* Only show profile and notification icons on small screens */}
      <div className="lg:hidden flex items-center gap-2">
        <img src={NotificationIcon} alt="Notification" className="w-8 h-8" />
      </div>

      {/* Show all icons on large screens */}
      <div className="hidden lg:flex items-center gap-4">
        <img src={HelpIcon} alt="Help" className="w-8 h-8" />
        <img src={DocumentsIcon} alt="Documents" className="w-8 h-8" />
        <img src={Languages} alt="Languages" className="w-8 h-8" />
        <SettingsDropdown
          onRecoveryKeyClick={() => setIsRecoveryKeyModalOpen(true)}
          onImportWalletClick={() => setIsImportWalletModalOpen(true)}
          onManageWalletsClick={() => setIsManageWalletsModalOpen(true)}
        />
        <img src={NotificationIcon} alt="Notification" className="w-8 h-8" />
      </div>
      
      {/* Profile Section with Dropdown */}
      <div className="relative" data-profile-menu>
        <button 
          onClick={() => setIsProfileOpen(!isProfileOpen)}
          className="flex items-center gap-2 p-2 rounded-lg hover:bg-[#1D2226] transition-colors"
        >
          <img src={ProfileIcon} alt="Profile" className="w-8 h-8" />
        </button>

        {/* Profile Dropdown */}
        {isProfileOpen && authenticated && (
          <div className="absolute right-0 mt-2 w-64 bg-[#101114] rounded-xl shadow-lg py-2 z-[999] text-white">
            <div className="px-4 py-3 border-b border-gray-700">
              <p className="text-white text-base font-medium">{userInfo.displayName}</p>
              {userInfo.email && typeof userInfo.email === 'string' ? (
                <p className="text-xs text-gray-400 truncate">{userInfo.email}</p>
              ) : userInfo.userId && typeof userInfo.userId === 'string' ? (
                <p className="text-xs text-gray-400 truncate">{userInfo.userId}</p>
              ) : userAddress && typeof userAddress === 'string' ? (
                <p className="text-xs text-gray-400 truncate">{userAddress}</p>
              ) : null}
            </div>

            {/* Display wallet addresses by chain with section dividers */}
            <div className="max-h-60 overflow-y-auto py-1">
              {addressesByChain.ethereum.length > 0 && (
                <div className="border-b border-gray-800">
                  <p className="text-sm text-gray-400 px-4 py-2">Ethereum</p>
                  {addressesByChain.ethereum.map((addr, i) => (
                    <div key={i} className="flex items-center justify-between px-4 py-2 hover:bg-[#1E1F24]">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-white truncate w-32">{formatAddress(addr.address)}</span>
                        {addr.walletType === 'smart' && (
                          <span className="text-xs px-1.5 py-0.5 rounded-md bg-green-900/30 text-green-400">Smart</span>
                        )}
                      </div>
                      <button
                        onClick={() => copyToClipboard(addr.address)}
                        className="p-1 text-gray-400 hover:text-white"
                      >
                        {copiedAddress === addr.address ? <FaCheck size={14} /> : <FaCopy size={14} />}
                      </button>
                    </div>
                  ))}
                </div>
              )}
              
              {addressesByChain.solana.length > 0 && (
                <div>
                  <p className="text-sm text-gray-400 px-4 py-2">Solana</p>
                  {addressesByChain.solana.map((addr, i) => (
                    <div key={i} className="flex items-center justify-between px-4 py-2 hover:bg-[#1E1F24]">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-white truncate w-32">{formatAddress(addr.address)}</span>
                        {addr.walletType === 'smart' && (
                          <span className="text-xs px-1.5 py-0.5 rounded-md bg-green-900/30 text-green-400">Smart</span>
                        )}
                      </div>
                      <button
                        onClick={() => copyToClipboard(addr.address)}
                        className="p-1 text-gray-400 hover:text-white"
                      >
                        {copiedAddress === addr.address ? <FaCheck size={14} /> : <FaCopy size={14} />}
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Add Passkey to Account button */}
            <div className="px-4 py-3 border-t border-gray-800 mt-1">
              <LinkPasskey />
            </div>

            <div className="px-4 py-3 border-t border-gray-800 mt-1">
              <button
                onClick={() => {
                  logout();
                  setIsProfileOpen(false);
                }}
                className="w-full text-left text-sm text-red-400 hover:text-red-300 transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>
    </div>

    {/* Mobile Menu (sliding panel) */}
    {mobileMenuOpen && (
      <div className="lg:hidden fixed inset-0 bg-[#141416] z-[998] flex flex-col pt-16">
        <div className="container mx-auto px-4">
          {/* Search in mobile menu */}
          <div className="relative mb-6">
            <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
              type="text"
              placeholder="Search..."
              className="bg-[#181C20] text-white pl-12 pr-4 py-3 rounded-3xl w-full outline-none"
              onClick={() => {
                setIsSearchOpen(true);
                setMobileMenuOpen(false);
              }}
              readOnly
            />
          </div>
          
          {/* Navigation links */}
          <ul className="flex flex-col gap-4 mb-8">
            {["Home", "Spot","Vault","Portfolio","Pulse"].map((tab) => (
              <li
                key={tab}
                onClick={() => handleTabClick(tab)}
                className={`px-4 py-3 rounded-xl font-medium cursor-pointer transition-all ${
                  activeTab === tab ? "bg-white text-gray-600" : "text-white"
                }`}
              >
                {tab}
              </li>
            ))}
          </ul>

          {/* Icons in mobile menu */}
          <div className="grid grid-cols-3 gap-6">
            <div className="flex flex-col items-center gap-2">
              <img src={HelpIcon} alt="Help" className="w-10 h-10" />
              <span className="text-sm">Help</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={DocumentsIcon} alt="Documents" className="w-10 h-10" />
              <span className="text-sm">Docs</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={Languages} alt="Languages" className="w-10 h-10" />
              <span className="text-sm">Language</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={SettingsIcon} alt="Settings" className="w-4 h-4" />
              
              <span className="text-sm">Settings</span>
            </div>
            <div className="flex flex-col items-center gap-2">
              <img src={NotificationIcon} alt="Notifications" className="w-10 h-10" />
              <span className="text-sm">Alerts</span>
            </div>
            {authenticated && (
              <div className="flex flex-col items-center gap-2">
                <button
                  onClick={() => {
                    logout();
                    setMobileMenuOpen(false);
                  }}
                  className="w-10 h-10 flex items-center justify-center bg-red-500/20 rounded-full"
                >
                  <FaTimes className="text-red-400" />
                </button>
                <span className="text-sm text-red-400">Sign Out</span>
              </div>
            )}
          </div>
        </div>
      </div>
    )}

    {isSearchOpen && (
      <SearchModal
        onClose={() => {
          setIsSearchOpen(false);
          setMobileMenuOpen(false);
        }}
      />
    )}

    {/* Recovery Key Modal */}
    <RecoveryKeyModal
      isOpen={isRecoveryKeyModalOpen}
      onClose={() => setIsRecoveryKeyModalOpen(false)}
    />

    {/* Import Wallet Modal */}
    <ImportWalletModal
      isOpen={isImportWalletModalOpen}
      onClose={() => setIsImportWalletModalOpen(false)}
    />

    {/* Manage Wallets Modal */}
    <ManageWalletsModal
      isOpen={isManageWalletsModalOpen}
      onClose={() => setIsManageWalletsModalOpen(false)}
    />
  </nav>
  );
};

export default Navbar;



