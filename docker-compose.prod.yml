version: '3.8'

services:
  frontend:
    build:
      context: ./spot_frontend
      dockerfile: Dockerfile
    container_name: redfyn-frontend
    restart: always
    ports:
      - "4000:80" # Map host port 4000 to container port 80 (nginx)
    volumes:
      # Mount source code for development (optional, enables hot reloading if configured)
      - ./spot_frontend:/app
      # Prevent node_modules from being overwritten by host volume
      - /app/node_modules
    env_file:
      - ./spot_frontend/.env
    networks:
      - app-network
    depends_on:
      - backend

  backend:
    build:
      context: ./spot_backend
      dockerfile: Dockerfile
    container_name: redfyn-backend
    restart: always
    ports:
      - "5000:5000" # Exposes backend on host port 5000, assumes container runs on 5000
    volumes:
      # Mount source code for development (optional, enables hot reloading if configured)
      - ./spot_backend:/app
      # Prevent node_modules from being overwritten by host volume
      - /app/node_modules
    command: >
      sh -c "npm run build && npm start"
    env_file:
      - ./spot_backend/.env
    environment:
      - NODE_ENV=production
      - PORT=5000
      - REDIS_URL=redis://redis:6379
    networks:
      - app-network
    depends_on:
      - redis

  liquidity_pool:
    build:
      context: ./liquidity_pool_v1
      dockerfile: Dockerfile
    container_name: redfyn-liquidity-pool
    restart: always
    ports:
      - "3000:3000" # Exposes liquidity pool service on host port 3000
    volumes:
      # Mount source code if needed for development
      - ./liquidity_pool_v1:/app
      - /app/node_modules
    command: >
      sh -c "npm run build && npm start"
    env_file:
      - ./liquidity_pool_v1/.env
    environment:
      - NODE_ENV=production
      - PORT=3000
      - REDIS_URL=redis://redis:6379
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    networks:
      - app-network
    depends_on:
      - redis

  redis:
    image: "redis:alpine"
    container_name: redfyn-redis
    restart: always
    volumes:
      - redis_data:/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  redis_data: 