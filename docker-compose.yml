version: '3.8'

services:
  frontend:
    build:
      context: ./spot_frontend
      dockerfile: Dockerfile
    ports:
      - "4000:80" # Map host port 4000 to container port 80 (nginx)
    volumes:
      # Mount source code for development (optional, enables hot reloading if configured)
      - ./spot_frontend:/app
      # Prevent node_modules from being overwritten by host volume
      - /app/node_modules
    env_file:
      - ./spot_frontend/.env
    networks:
      - app-network
    depends_on:
      - backend # Optional: waits for backend to start

  backend:
    build:
      context: ./spot_backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000" # Exposes backend on host port 5000, assumes container runs on 5000
    volumes:
      # Mount source code for development (optional, enables hot reloading if configured)
      - ./spot_backend:/app
      # Prevent node_modules from being overwritten by host volume
      - /app/node_modules
    command: >
      sh -c "npm run build && npm start"
    env_file:
      - ./spot_backend/.env
    environment:
      # Override Redis configuration to ensure it connects to the redis service
      - REDIS_URL=redis://redis:6379
      # Ensure the backend listens on port 5000, overriding any .env setting or default
      - PORT=5000
    networks:
      - app-network
    depends_on:
      - redis
    # If backend depends on a database service (add db service below if needed)
    # depends_on:
    #   - db 

  liquidity_pool:
    build:
      context: ./liquidity_pool_v1 # Assuming this directory exists
      dockerfile: Dockerfile
    ports:
      - "3000:3000" # Exposes liquidity pool service on host port 3000
    volumes:
      # Mount source code if needed for development
      - ./liquidity_pool_v1:/app
      - /app/node_modules
    command: >
      sh -c "npm run build && npm start"
    env_file:
      - ./liquidity_pool_v1/.env
    networks:
      - app-network
    depends_on:
      - redis

  redis:
    image: "redis:alpine"
    ports:
      # Optional: Expose Redis port to host if needed for external debugging/clients
      - "6379:6379" # Example - uncomment if needed, keep the dash for list format
    networks:
      - app-network
    volumes:
      - redis_data:/data # Persist Redis data

# Define the network for services to communicate
networks:
  app-network:
    driver: bridge

# Optional: Define volumes if needed (e.g., for database persistence)
# volumes:
#   postgres_data: 

volumes:
  redis_data: # Define the volume for Redis persistence
  # postgres_data: # Uncomment if using a db service