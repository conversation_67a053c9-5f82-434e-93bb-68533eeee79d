version: '3.8'

services:
  # Traefik reverse proxy with automatic SSL
  traefik:
    image: traefik:v2.10
    container_name: redfyn-traefik
    restart: unless-stopped
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --certificatesresolvers.letsencrypt.acme.httpchallenge=true
      - --certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-ssl-certs:/letsencrypt
    networks:
      - app-network

  # Redis service
  redis:
    image: redis:7-alpine
    container_name: redfyn-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes

  # Solana Service
  solana:
    build:
      context: ./backend/solana
      dockerfile: Dockerfile
    container_name: redfyn-solana
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=6001
    env_file:
      - ./backend/solana/.env.production
    networks:
      - app-network
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.solana.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/solana-api`)"
      - "traefik.http.routers.solana.entrypoints=websecure"
      - "traefik.http.routers.solana.tls.certresolver=letsencrypt"
      - "traefik.http.services.solana.loadbalancer.server.port=6001"
      - "traefik.http.middlewares.solana-stripprefix.stripprefix.prefixes=/solana-api"
      - "traefik.http.routers.solana.middlewares=solana-stripprefix"

  # Liquidity Pool Service
  liquidity-pool:
    build:
      context: ./backend/liquidity_pool
      dockerfile: Dockerfile
    container_name: redfyn-liquidity-pool
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3047
      - REDIS_URL=redis://redis:6379
    env_file:
      - ./backend/liquidity_pool/.env.production
    networks:
      - app-network
    depends_on:
      - redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.liquidity.rule=Host(`redfyn.crypfi.io`) && PathPrefix(`/liquidity-api`)"
      - "traefik.http.routers.liquidity.entrypoints=websecure"
      - "traefik.http.routers.liquidity.tls.certresolver=letsencrypt"
      - "traefik.http.services.liquidity.loadbalancer.server.port=3047"
      - "traefik.http.middlewares.liquidity-stripprefix.stripprefix.prefixes=/liquidity-api"
      - "traefik.http.routers.liquidity.middlewares=liquidity-stripprefix"

  # Spot Backend Service
  spot-backend:
    build:
      context: ./backend/spot_backend
      dockerfile: Dockerfile
    container_name: redfyn-spot-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5001
      - REDIS_URL=redis://redis:6379
    env_file:
      - ./backend/spot_backend/.env.production
    networks:
      - app-network
    depends_on:
      - redis
      - liquidity-pool
      - solana
    labels:
      - "traefik.enable=true"
      # API routes
      - "traefik.http.routers.backend.rule=Host(`redfyn.crypfi.io`) && (PathPrefix(`/api`) || PathPrefix(`/socket.io/`))"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=5001"

  # Frontend Service with Nginx
  frontend:
    build:
      context: ./spot_frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    container_name: redfyn-frontend
    restart: unless-stopped
    env_file:
      - ./spot_frontend/.env.production
    networks:
      - app-network
    depends_on:
      - spot-backend
      - liquidity-pool
      - solana
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`redfyn.crypfi.io`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"
      # Redirect HTTP to HTTPS
      - "traefik.http.routers.frontend-http.rule=Host(`redfyn.crypfi.io`)"
      - "traefik.http.routers.frontend-http.entrypoints=web"
      - "traefik.http.routers.frontend-http.middlewares=redirect-to-https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  traefik-ssl-certs:
    driver: local
