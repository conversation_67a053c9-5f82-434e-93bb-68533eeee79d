version: '3.8'

services:
  # Redis service for caching
  redis:
    image: redis:7-alpine
    container_name: redfyn-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    command: redis-server --appendonly yes

  # Solana Service
  solana:
    build:
      context: ./backend/solana
      dockerfile: Dockerfile
    container_name: redfyn-solana
    restart: always
    ports:
      - "6001:6001"
    env_file:
      - ./backend/solana/.env.production
    environment:
      - NODE_ENV=production
      - PORT=6001
    networks:
      - app-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Liquidity Pool Service
  liquidity-pool:
    build:
      context: ./backend/liquidity_pool
      dockerfile: Dockerfile
    container_name: redfyn-liquidity-pool
    restart: always
    ports:
      - "3047:3047"
    env_file:
      - ./backend/liquidity_pool/.env.production
    environment:
      - NODE_ENV=production
      - PORT=3047
      - REDIS_URL=redis://redis:6379
    networks:
      - app-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3047/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Spot Backend Service
  spot-backend:
    build:
      context: ./backend/spot_backend
      dockerfile: Dockerfile
    container_name: redfyn-spot-backend
    restart: always
    ports:
      - "5001:5001"
    env_file:
      - ./backend/spot_backend/.env.production
    environment:
      - NODE_ENV=production
      - PORT=5001
      - REDIS_URL=redis://redis:6379
    networks:
      - app-network
    depends_on:
      - redis
      - liquidity-pool
      - solana
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: ./spot_frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    container_name: redfyn-frontend
    restart: always
    ports:
      - "80:80"
      - "443:443"
    env_file:
      - ./spot_frontend/.env.production
    networks:
      - app-network
    depends_on:
      - spot-backend
      - liquidity-pool
      - solana
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
